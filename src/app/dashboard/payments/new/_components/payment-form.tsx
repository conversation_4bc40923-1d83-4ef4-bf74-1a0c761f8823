"use client";

import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import {
    Card,
    CardContent,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Check, ChevronsUpDown } from "lucide-react";
import { formatCurrency, cn } from "@/lib/utils";
import { clearAllCache } from "@/hooks/useCachedFetch";
import {
    Command,
    CommandEmpty,
    CommandGroup,
    CommandInput,
    CommandItem,
} from "@/components/ui/command";
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from "@/components/ui/popover";

// Define types for customers and orders
type Customer = {
    id: string;
    name: string;
    phone?: string | null;
    email?: string | null;
    currentBalance?: number | null;
};

type Order = {
    id: string;
    memoNo: string;
    date: string;
    totalAmount: number;
    paidAmount: number;
    dueAmount: number;
    customer?: Customer | null;
};

// Props type for the PaymentForm component
interface PaymentFormProps {
    customers: Customer[];
    selectedCustomer: Customer | null;
    selectedOrder: Order | null;
    tenantId: string;
}

export function PaymentForm({ customers, selectedCustomer, selectedOrder, tenantId }: PaymentFormProps) {
    const router = useRouter();
    const [date, setDate] = useState<string>(new Date().toISOString().split('T')[0]);
    const [customerId, setCustomerId] = useState<string>("");
    const [customerName, setCustomerName] = useState<string>("");
    const [customerPhone, setCustomerPhone] = useState<string>("");
    const [currentDue, setCurrentDue] = useState<number>(0);
    const [paymentAmount, setPaymentAmount] = useState<number>(0);
    const [orderId, setOrderId] = useState<string>("");
    const [notes, setNotes] = useState<string>("");
    const [paymentMethod, setPaymentMethod] = useState<string>("");
    const [paymentReference, setPaymentReference] = useState<string>("");
    const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
    const [isMounted, setIsMounted] = useState<boolean>(false);
    const paymentMethodRef = useRef<HTMLButtonElement>(null);

    // Client-side only rendering to prevent hydration issues
    useEffect(() => {
        setIsMounted(true);
    }, []);

    // Initialize form with selected customer if any
    useEffect(() => {
        if (selectedCustomer) {
            setCustomerId(selectedCustomer.id);
            setCustomerName(selectedCustomer.name);
            setCustomerPhone(selectedCustomer.phone || "");
            setCurrentDue(selectedCustomer.currentBalance || 0);

            // Default payment to full amount
            if (selectedCustomer.currentBalance) {
                setPaymentAmount(selectedCustomer.currentBalance);
            }
        }
    }, [selectedCustomer]);

    // Initialize form with selected order if any
    useEffect(() => {
        if (selectedOrder) {
            setOrderId(selectedOrder.id);
            if (selectedOrder.dueAmount > 0) {
                setPaymentAmount(selectedOrder.dueAmount);
                setNotes(`Payment for order ${selectedOrder.memoNo}`);
            }
        }
    }, [selectedOrder]);

    // Handle customer selection
    const handleCustomerChange = (customerId: string) => {
        setCustomerId(customerId);

        if (customerId) {
            const selectedCustomer = customers.find((c: Customer) => c.id === customerId);
            if (selectedCustomer) {
                setCustomerName(selectedCustomer.name);
                setCustomerPhone(selectedCustomer.phone || "");
                setCurrentDue(selectedCustomer.currentBalance || 0);
                setPaymentAmount(selectedCustomer.currentBalance || 0);
            }
        } else {
            setCustomerName("");
            setCustomerPhone("");
            setCurrentDue(0);
            setPaymentAmount(0);
        }
    };

    // Handle payment amount change
    const handlePaymentAmountChange = (amount: number) => {
        if (amount > currentDue) {
            amount = currentDue;
            toast.error(`Cannot pay more than the current due amount: ${formatCurrency(currentDue)}`);
        }
        setPaymentAmount(amount);
    };

    // Handle form submission
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        // Validate the form
        if (!customerId) {
            toast.error("Customer is required");
            return;
        }

        if (paymentAmount <= 0) {
            toast.error("Payment amount must be greater than zero");
            return;
        }

        if (paymentAmount > currentDue) {
            toast.error("Payment amount cannot exceed current due");
            return;
        }

        if (!paymentMethod) {
            toast.error("Payment method is required");
            // Scroll to the payment method field
            if (paymentMethodRef.current) {
                paymentMethodRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' });
                paymentMethodRef.current.focus();
            }
            return;
        }

        // Prepare the payment data
        const paymentData = {
            tenantId,
            customerId,
            orderId: orderId || null,
            amount: paymentAmount,
            paymentMethod,
            paymentReference,
            notes,
            date: new Date(date).toISOString()
        };

        try {
            setIsSubmitting(true);
            const response = await fetch("/api/payments", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(paymentData),
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.message || "Something went wrong");
            }

            toast.success("Payment recorded successfully");

            // Clear all client-side cache to ensure fresh data
            clearAllCache();

            // Add a small delay before redirecting to ensure cache is cleared
            setTimeout(() => {
                // If we have an order ID, redirect to that order's page
                if (orderId) {
                    // Force a server-side refresh by adding a timestamp parameter
                    const timestamp = Date.now();
                    router.push(`/dashboard/sales?_refresh=${timestamp}`);
                } else {
                    router.push("/dashboard/sales");
                }
                router.refresh();
            }, 300);
        } catch (error: any) {
            toast.error(error.message || "An error occurred");
        } finally {
            setIsSubmitting(false);
        }
    };

    // Handle cancel
    const handleCancel = () => {
        router.push("/dashboard/sales");
    };

    // If not mounted yet, show a loading state
    if (!isMounted) {
        return (
            <div className="bg-sky-100 p-6 rounded-md">
                <h1 className="text-2xl font-bold text-center mb-6">Record Customer Payment</h1>
                <div className="min-h-[300px] flex items-center justify-center">
                    <p>Loading payment form...</p>
                </div>
            </div>
        );
    }

    return (
        <form onSubmit={handleSubmit} className="bg-sky-100 p-6 rounded-md" suppressHydrationWarning>
            <h1 className="text-2xl font-bold text-center mb-6">Record Customer Payment</h1>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div>
                    <label className="block text-sm font-medium mb-1">Date</label>
                    <Input
                        type="date"
                        value={date}
                        onChange={(e) => setDate(e.target.value)}
                        className="bg-white"
                    />
                </div>
                <div>
                    <label className="block text-sm font-medium mb-1">Customer</label>
                    <Popover>
                        <PopoverTrigger asChild>
                            <Button
                                variant="outline"
                                role="combobox"
                                className="w-full justify-between bg-white"
                                disabled={!!selectedCustomer}
                            >
                                {customerId
                                    ? customers.find((customer: Customer) => customer.id === customerId)?.name
                                    : "Select customer..."}
                                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                            </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-full p-0">
                            <Command>
                                <CommandInput placeholder="Search customer..." />
                                <CommandEmpty>No customer found.</CommandEmpty>
                                <CommandGroup>
                                    {customers.map((customer: Customer) => (
                                        <CommandItem
                                            key={customer.id}
                                            value={customer.id}
                                            onSelect={() => {
                                                handleCustomerChange(customer.id);
                                            }}
                                        >
                                            <Check
                                                className={cn(
                                                    "mr-2 h-4 w-4",
                                                    customerId === customer.id ? "opacity-100" : "opacity-0"
                                                )}
                                            />
                                            {customer.name} {customer.phone ? `(${customer.phone})` : ""}
                                            {customer.currentBalance && customer.currentBalance > 0 ? (
                                                <span className="ml-2 text-xs bg-red-100 text-red-800 px-1 py-0.5 rounded">
                                                    Due: {formatCurrency(customer.currentBalance)}
                                                </span>
                                            ) : null}
                                        </CommandItem>
                                    ))}
                                </CommandGroup>
                            </Command>
                        </PopoverContent>
                    </Popover>
                </div>
            </div>

            {customerId && (
                <div className="mb-6">
                    <div className="bg-white p-4 rounded-md mb-4">
                        <div className="flex justify-between mb-2">
                            <div className="font-medium">Customer:</div>
                            <div>{customerName}</div>
                        </div>
                        {customerPhone && (
                            <div className="flex justify-between mb-2">
                                <div className="font-medium">Phone:</div>
                                <div>{customerPhone}</div>
                            </div>
                        )}
                        <div className="flex justify-between mb-2">
                            <div className="font-medium">Current Due:</div>
                            <div className="text-lg font-bold text-red-600">{formatCurrency(currentDue)}</div>
                        </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                            <label className="block text-sm font-medium mb-1">
                                Payment Method <span className="text-red-500">*</span>
                            </label>
                            <Select value={paymentMethod} onValueChange={setPaymentMethod}>
                                <SelectTrigger
                                    ref={paymentMethodRef}
                                    className={`bg-white ${!paymentMethod ? "border-red-500 focus:ring-red-500" : ""}`}
                                    suppressHydrationWarning
                                >
                                    <SelectValue placeholder="Select payment method" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="cash">Cash</SelectItem>
                                    <SelectItem value="card">Card</SelectItem>
                                    <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                                </SelectContent>
                            </Select>
                            {!paymentMethod && (
                                <p className="text-sm text-red-500 mt-1">Please select a payment method</p>
                            )}
                        </div>
                        <div>
                            <label className="block text-sm font-medium mb-1">Payment Reference</label>
                            <Input
                                type="text"
                                value={paymentReference}
                                onChange={(e) => setPaymentReference(e.target.value)}
                                placeholder="Optional reference number, transaction ID, etc."
                                className="bg-white"
                                suppressHydrationWarning
                            />
                        </div>
                    </div>

                    <div className="mb-4">
                        <label className="block text-sm font-medium mb-1">Payment Amount</label>
                        <div className="relative">
                            <Input
                                type="number"
                                min="0"
                                max={currentDue}
                                step="0.01"
                                value={paymentAmount}
                                onChange={(e) => handlePaymentAmountChange(Number(e.target.value))}
                                className="bg-white text-lg font-bold pr-16"
                            />
                            <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                <span className="text-gray-500">৳</span>
                            </div>
                        </div>
                        {paymentAmount > 0 && (
                            <div className="mt-2 text-sm text-gray-600">
                                Remaining after this payment: {formatCurrency(currentDue - paymentAmount)}
                            </div>
                        )}
                    </div>

                    <div className="mb-4">
                        <label className="block text-sm font-medium mb-1">Notes</label>
                        <Textarea
                            value={notes}
                            onChange={(e) => setNotes(e.target.value)}
                            placeholder="Optional notes about this payment"
                            className="bg-white"
                        />
                    </div>
                </div>
            )}

            <div className="flex justify-end space-x-4 mt-6">
                <Button
                    type="button"
                    variant="outline"
                    onClick={handleCancel}
                    className="cursor-pointer"
                >
                    Cancel
                </Button>
                <Button
                    type="submit"
                    disabled={isSubmitting || !customerId || paymentAmount <= 0 || !paymentMethod}
                    className="bg-blue-600 hover:bg-blue-700 text-white cursor-pointer"
                >
                    {isSubmitting ? "Recording Payment..." : "Record Payment"}
                </Button>
            </div>
        </form>
    );
}
