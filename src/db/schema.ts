import { sqliteTable, text, integer, real } from "drizzle-orm/sqlite-core";
import { relations } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";

// User model
export const users = sqliteTable("users", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => uuidv4()),
  username: text("username").notNull(),
  password: text("password").notNull(),
  email: text("email").unique(),
  phone: text("phone"),
  fullName: text("full_name").notNull(),
  address: text("address"),
  role: text("role")
    .notNull()
    .default("tenant")
    .$type<"admin" | "tenant" | "tenant_sale" | "vendor">(),
  isActive: integer("is_active", { mode: "boolean" }).default(true),
  lastLogin: integer("last_login", { mode: "timestamp" }),
  createdAt: integer("created_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
  updatedAt: integer("updated_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
});

// Define users relationships
export const usersRelations = relations(users, ({ many }) => ({
  tenantOwned: many(tenants),
  ownedTenantUsers: many(tenantUsers, { relationName: "ownedTenantUsers" }),
  memberTenantUsers: many(tenantUsers, { relationName: "memberTenantUsers" }),
  sessions: many(sessions),
  menuPermissions: many(userMenuPermissions, { relationName: "tenantMenuPermissions" }),
  refundsPerformed: many(refunds, { relationName: "refundPerformer" }),
}));

// Tenants table for multi-tenant architecture[only use for license ]
export const tenants = sqliteTable("tenant_license_management", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => uuidv4()),
  userId: text("user_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  domain: text("domain"),
  companyName: text("company_name").notNull(),
  logoUrl: text("logo_url"),
  isActive: integer("is_active", { mode: "boolean" }).default(true),
  subscriptionType: text("subscription_type")
    .notNull()
    .default("free-trial")
    .$type<"free-trial" | "paid">(),
  subscriptionStartDate: integer("subscription_start_date", {
    mode: "timestamp",
  }).$defaultFn(() => new Date()),
  subscriptionEndDate: integer("subscription_end_date", {
    mode: "timestamp",
  }).$defaultFn(() => {
    const date = new Date();
    date.setDate(date.getDate() + 7); // Default is 7 days free trial
    return date;
  }),
  paymentStatus: text("payment_status")
    .default("pending")
    .$type<"pending" | "paid" | "overdue">(),
  lastPaymentDate: integer("last_payment_date", { mode: "timestamp" }),
  paymentDueDate: integer("payment_due_date", { mode: "timestamp" }),
  settings: text("settings"), // JSON string for tenant-specific settings
  createdAt: integer("created_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
  updatedAt: integer("updated_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
});

// Define tenant relationships
export const tenantsRelations = relations(tenants, ({ one, many }) => ({
  owner: one(users, {
    fields: [tenants.userId],
    references: [users.id],
  }),
  products: many(products),
  categories: many(productCategories),
  vendors: many(vendors),
  customers: many(customers),
  inventory: many(inventoryTransactions, { relationName: "inventoryTransactions" }),
  orders: many(orders),
  payments: many(payments),
  branches: many(branches),
  expenseCategories: many(expenseCategories),
}));

// Branches table for multi-branch inventory management
export const branches = sqliteTable("branches", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => uuidv4()),
  tenantId: text("tenant_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  name: text("name").notNull(),
  code: text("code").notNull(),
  address: text("address"),
  phone: text("phone"),
  email: text("email"),
  isMain: integer("is_main", { mode: "boolean" }).default(false),
  isActive: integer("is_active", { mode: "boolean" }).default(true),
  notes: text("notes"),
  createdAt: integer("created_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
  updatedAt: integer("updated_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
});

// Define branch relationships
export const branchesRelations = relations(branches, ({ one, many }) => ({
  tenant: one(users, {
    fields: [branches.tenantId],
    references: [users.id],
  }),
  tenantLicense: one(tenants, {
    fields: [branches.tenantId],
    references: [tenants.userId],
    relationName: "branches"
  }),
  branchUsers: many(branchUsers),
  inventory: many(inventoryTransactions, { relationName: "branchInventory" }),
  // These relations were removed as source_branch_id and target_branch_id columns don't exist
  // sourceTransactions: many(inventoryTransactions, { relationName: "sourceTransactions" }),
  // targetTransactions: many(inventoryTransactions, { relationName: "targetTransactions" }),
  orders: many(orders),
  payments: many(payments),
  expenses: many(expenses),
}));

// Junction table for users and branches with roles
export const branchUsers = sqliteTable("branch_users", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => uuidv4()),
  branchId: text("branch_id")
    .notNull()
    .references(() => branches.id, { onDelete: "cascade" }),
  userId: text("user_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  isActive: integer("is_active", { mode: "boolean" }).default(true),
  createdAt: integer("created_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
  updatedAt: integer("updated_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
});

// Define branch users relationships
export const branchUsersRelations = relations(branchUsers, ({ one }) => ({
  branch: one(branches, {
    fields: [branchUsers.branchId],
    references: [branches.id],
  }),
  user: one(users, {
    fields: [branchUsers.userId],
    references: [users.id],
  }),
}));

// Junction table for users and tenants with roles
export const tenantUsers = sqliteTable("tenant_users", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => uuidv4()),
  tenantId: text("tenant_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  userId: text("user_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  role: text("role").notNull().$type<"owner" | "admin" | "manager" | "staff">(),
  isActive: integer("is_active", { mode: "boolean" }).default(true),
  createdAt: integer("created_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
  updatedAt: integer("updated_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
});

// Define tenant users relationships
export const tenantUsersRelations = relations(tenantUsers, ({ one }) => ({
  tenant: one(users, {
    fields: [tenantUsers.tenantId],
    references: [users.id],
    relationName: "ownedTenantUsers"
  }),
  user: one(users, {
    fields: [tenantUsers.userId],
    references: [users.id],
    relationName: "memberTenantUsers"
  }),
}));

// Product categories (publishers)
export const productCategories = sqliteTable("product_categories", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => uuidv4()),
  tenantId: text("tenant_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  name: text("name").notNull(),
  description: text("description"),
  isActive: integer("is_active", { mode: "boolean" }).default(true),
  createdAt: integer("created_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
  updatedAt: integer("updated_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
});

// Define product categories relationships
export const productCategoriesRelations = relations(
  productCategories,
  ({ one, many }) => ({
    tenant: one(users, {
      fields: [productCategories.tenantId],
      references: [users.id],
    }),
    tenantLicense: one(tenants, {
      fields: [productCategories.tenantId],
      references: [tenants.userId],
      relationName: "categories"
    }),
    products: many(products),
  })
);
// Vendors/Suppliers table (authors)
export const vendors = sqliteTable("vendors", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => uuidv4()),
  tenantId: text("tenant_id")
    .notNull()
    .references(() => users.id, {
      onDelete: "cascade",
    }),
  userId: text("user_id").references(() => users.id, { onDelete: "cascade" }),
  vendorCode: text("vendor_code"),
  name: text("name").notNull(),
  email: text("email"),
  phone: text("phone"),
  address: text("address"),
  isActive: integer("is_active", { mode: "boolean" }).default(true),
  createdAt: integer("created_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
  updatedAt: integer("updated_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
});

// Define vendors relationships
export const vendorsRelations = relations(vendors, ({ one, many }) => ({
  tenant: one(users, {
    fields: [vendors.tenantId],
    references: [users.id],
  }),
  tenantLicense: one(tenants, {
    fields: [vendors.tenantId],
    references: [tenants.userId],
    relationName: "vendors"
  }),
  user: one(users, {
    // Define relation to users
    fields: [vendors.userId],
    references: [users.id],
  }),
  products: many(products),
  royaltyTransactions: many(royaltyTransactions),
}));

// Products table (books)
export const products = sqliteTable("products", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => uuidv4()),
  tenantId: text("tenant_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  code: text("code"),
  name: text("name").notNull(),
  description: text("description"),
  categoryId: text("category_id").references(() => productCategories.id),
  vendorId: text("vendor_id").references(() => vendors.id),
  unit: text("unit").notNull(),
  costPrice: real("cost_price").notNull(),
  sellingPrice: real("selling_price").notNull(),
  discount: real("discount").default(0),
  royaltyType: text("royalty_type")
    .$type<"none" | "fixed" | "percentage">()
    .default("none"),
  royaltyValue: real("royalty_value").default(0),
  sku: text("sku"),
  weight: real("weight").default(0),
  isActive: integer("is_active", { mode: "boolean" }).default(true),
  createdAt: integer("created_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
  updatedAt: integer("updated_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
});

// Define products relationships
export const productsRelations = relations(products, ({ one, many }) => ({
  tenant: one(users, {
    fields: [products.tenantId],
    references: [users.id],
  }),
  tenantLicense: one(tenants, {
    fields: [products.tenantId],
    references: [tenants.userId],
    relationName: "products"
  }),
  category: one(productCategories, {
    fields: [products.categoryId],
    references: [productCategories.id],
  }),
  vendor: one(vendors, {
    fields: [products.vendorId],
    references: [vendors.id],
  }),
  inventoryTransactions: many(inventoryTransactions, { relationName: "product" }),
  royaltyTransactions: many(royaltyTransactions),
  transactionItems: many(stockTransactionItems, { relationName: "transactionItems" }),
}));

// Inventory transactions [stock in and out]
export const inventoryTransactions = sqliteTable("inventory_transactions", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => uuidv4()),
  tenantId: text("tenant_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  branchId: text("branch_id")
    .notNull()
    .references(() => branches.id, { onDelete: "cascade" }),
  productId: text("product_id")
    .notNull()
    .references(() => products.id, { onDelete: "cascade" }),
  quantity: integer("quantity").notNull(),
  warehouseQuantity: integer("warehouse_quantity").default(0),
  storeQuantity: integer("store_quantity").default(0),
  unitPrice: real("unit_price").notNull(),
  type: text("type").notNull().$type<"in" | "out" | "sale" | "transfer" | "refund">(),
  // These fields were removed in migration 0002_slim_purifiers.sql
  // Do not uncomment unless you add them back to the database
  // sourceBranchId: text("source_branch_id").references(() => branches.id),
  // targetBranchId: text("target_branch_id").references(() => branches.id),
  date: integer("date", { mode: "timestamp" }).notNull(),
  notes: text("notes"),
  performedBy: text("performed_by").references(() => users.id),
  createdAt: integer("created_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
  updatedAt: integer("updated_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
});

// Define inventory transactions relationships
export const inventoryTransactionsRelations = relations(
  inventoryTransactions,
  ({ one, many }) => ({
    tenant: one(users, {
      fields: [inventoryTransactions.tenantId],
      references: [users.id],
      relationName: "inventoryTenant"
    }),
    tenantLicense: one(tenants, {
      fields: [inventoryTransactions.tenantId],
      references: [tenants.userId],
      relationName: "inventoryTransactions"
    }),
    branch: one(branches, {
      fields: [inventoryTransactions.branchId],
      references: [branches.id],
      relationName: "branchInventory"
    }),
    // These relations were removed along with their fields in migration 0002_slim_purifiers.sql
    // Do not uncomment unless you add the fields back to the database
    // sourceBranch: one(branches, {
    //   fields: [inventoryTransactions.sourceBranchId],
    //   references: [branches.id],
    //   relationName: "sourceTransactions"
    // }),
    // targetBranch: one(branches, {
    //   fields: [inventoryTransactions.targetBranchId],
    //   references: [branches.id],
    //   relationName: "targetTransactions"
    // }),
    product: one(products, {
      fields: [inventoryTransactions.productId],
      references: [products.id],
      relationName: "product"
    }),
    performer: one(users, {
      fields: [inventoryTransactions.performedBy],
      references: [users.id],
      relationName: "inventoryPerformer"
    }),
    stockItems: many(stockTransactionItems, { relationName: "stockItems" }),
    royaltyTransactions: many(royaltyTransactions, { relationName: "royaltyTransactions" }),
  })
);

// Customers table
export const customers = sqliteTable("customers", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => uuidv4()),
  tenantId: text("tenant_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  code: text("code"),
  name: text("name").notNull(),
  phone: text("phone"),
  email: text("email"),
  address: text("address"),
  type: text("type").default("retail").$type<"retail" | "wholesale">(),
  creditLimit: real("credit_limit").default(0),
  currentBalance: real("current_balance").default(0),
  extraCommission: real("extra_commission").default(0),
  isActive: integer("is_active", { mode: "boolean" }).default(true),
  notes: text("notes"),
  createdAt: integer("created_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
  updatedAt: integer("updated_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
});

// Define customers relationships
export const customersRelations = relations(customers, ({ one }) => ({
  tenant: one(users, {
    fields: [customers.tenantId],
    references: [users.id],
  }),
  tenantLicense: one(tenants, {
    fields: [customers.tenantId],
    references: [tenants.userId],
    relationName: "customers"
  }),
}));

// Stock transaction items
export const stockTransactionItems = sqliteTable("stock_transaction_items", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => uuidv4()),
  transactionId: text("transaction_id")
    .notNull()
    .references(() => inventoryTransactions.id, { onDelete: "cascade" }),
  productId: text("product_id")
    .notNull()
    .references(() => products.id),
  quantity: integer("quantity").notNull(),
  unitPrice: real("unit_price").notNull(),
  discount: real("discount").default(0),
  tax: real("tax").default(0),
  total: real("total").notNull(),
  createdAt: integer("created_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
  updatedAt: integer("updated_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
});

// Define stock transaction items relationships
export const stockTransactionItemsRelations = relations(stockTransactionItems, ({ one }) => ({
  transaction: one(inventoryTransactions, {
    fields: [stockTransactionItems.transactionId],
    references: [inventoryTransactions.id],
    relationName: "stockItems"
  }),
  product: one(products, {
    fields: [stockTransactionItems.productId],
    references: [products.id],
    relationName: "transactionItems"
  }),
}));

// Royalty transactions to track payments to vendors [when inventory is out then royalty is inserted]
export const royaltyTransactions = sqliteTable("royalty_transactions", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => uuidv4()),
  vendorId: text("vendor_id")
    .notNull()
    .references(() => vendors.id),
  productId: text("product_id")
    .notNull()
    .references(() => products.id),
  transactionId: text("transaction_id").references(
    () => inventoryTransactions.id
  ),
  saleAmount: real("sale_amount").notNull(),
  royaltyAmount: real("royalty_amount").notNull(),
  isPaid: integer("is_paid", { mode: "boolean" }).default(false),
  paymentDate: integer("payment_date", { mode: "timestamp" }),
  notes: text("notes"),
  createdAt: integer("created_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
  updatedAt: integer("updated_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
});

// Define royalty transactions relationships
export const royaltyTransactionsRelations = relations(
  royaltyTransactions,
  ({ one }) => ({
    vendor: one(vendors, {
      fields: [royaltyTransactions.vendorId],
      references: [vendors.id],
    }),
    product: one(products, {
      fields: [royaltyTransactions.productId],
      references: [products.id],
    }),
    transaction: one(inventoryTransactions, {
      fields: [royaltyTransactions.transactionId],
      references: [inventoryTransactions.id],
      relationName: "royaltyTransactions"
    }),
  })
);

// Orders table for sales
export const orders = sqliteTable("orders", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => uuidv4()),
  tenantId: text("tenant_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  branchId: text("branch_id")
    .notNull()
    .references(() => branches.id, { onDelete: "cascade" }),
  customerId: text("customer_id").references(() => customers.id),
  memoNo: text("memo_no").notNull(),
  date: integer("date", { mode: "timestamp" })
    .notNull()
    .$defaultFn(() => new Date()),
  status: text("status")
    .notNull()
    .default("completed")
    .$type<"pending" | "completed" | "cancelled" | "draft">(),
  paymentMethod: text("payment_method")
    .notNull()
    .default("cash")
    .$type<"cash" | "card" | "bank_transfer">(),
  paymentStatus: text("payment_status")
    .notNull()
    .default("paid")
    .$type<"paid" | "partial" | "unpaid">(),
  subTotal: real("sub_total").notNull(),
  discountPercentage: real("discount_percentage").default(0),
  discountAmount: real("discount_amount").default(0),
  totalAmount: real("total_amount").notNull(),
  paidAmount: real("paid_amount").notNull(),
  dueAmount: real("due_amount").default(0),
  previousDue: real("previous_due").default(0),
  extCommission: real("ext_commission").default(0),
  extCommissionType: text("ext_commission_type").default("percentage").$type<"fixed" | "percentage">(),
  notes: text("notes"),
  performedBy: text("performed_by").references(() => users.id),
  createdAt: integer("created_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
  updatedAt: integer("updated_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
});

// Order items for each product in an order
export const orderItems = sqliteTable("order_items", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => uuidv4()),
  orderId: text("order_id")
    .notNull()
    .references(() => orders.id, { onDelete: "cascade" }),
  productId: text("product_id")
    .notNull()
    .references(() => products.id),
  quantity: integer("quantity").notNull(),
  unitPrice: real("unit_price").notNull(),
  discountPercentage: real("discount_percentage").default(0),
  discountAmount: real("discount_amount").default(0),
  total: real("total").notNull(),
  createdAt: integer("created_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
  updatedAt: integer("updated_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
});

// Define order relationships
export const ordersRelations = relations(orders, ({ one, many }) => ({
  tenant: one(users, {
    fields: [orders.tenantId],
    references: [users.id],
  }),
  tenantLicense: one(tenants, {
    fields: [orders.tenantId],
    references: [tenants.userId],
    relationName: "orders"
  }),
  branch: one(branches, {
    fields: [orders.branchId],
    references: [branches.id],
  }),
  customer: one(customers, {
    fields: [orders.customerId],
    references: [customers.id],
  }),
  items: many(orderItems),
  performer: one(users, {
    fields: [orders.performedBy],
    references: [users.id],
  }),
}));

// Define order items relationships
export const orderItemsRelations = relations(orderItems, ({ one }) => ({
  order: one(orders, {
    fields: [orderItems.orderId],
    references: [orders.id],
  }),
  product: one(products, {
    fields: [orderItems.productId],
    references: [products.id],
  }),
}));

// Sessions for authentication
export const sessions = sqliteTable("sessions", {
  id: text("id").primaryKey(),
  userId: text("user_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  expiresAt: integer("expires_at").notNull(),
  createdAt: integer("created_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
});

// Define sessions relationships
export const sessionsRelations = relations(sessions, ({ one }) => ({
  user: one(users, {
    fields: [sessions.userId],
    references: [users.id],
  }),
}));

// User menu permissions table - menuId now refers to the menu ID from the file, not the database
export const userMenuPermissions = sqliteTable("user_menu_permissions", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => uuidv4()),
  userId: text("user_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  tenantId: text("tenant_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  menuId: text("menu_id").notNull(), // Menu ID from the menu-list.ts file
  canView: integer("can_view", { mode: "boolean" }).default(true),
  canCreate: integer("can_create", { mode: "boolean" }).default(false),
  canEdit: integer("can_edit", { mode: "boolean" }).default(false),
  canDelete: integer("can_delete", { mode: "boolean" }).default(false),
  createdAt: integer("created_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
  updatedAt: integer("updated_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
});

// Define user menu permissions relationships
export const userMenuPermissionsRelations = relations(userMenuPermissions, ({ one }) => ({
  user: one(users, {
    fields: [userMenuPermissions.userId],
    references: [users.id],
  }),
  tenant: one(users, {
    fields: [userMenuPermissions.tenantId],
    references: [users.id],
    relationName: "tenantMenuPermissions"
  }),
  // menu relation removed as menus are now loaded from file
}));

// Payment transactions to track all payments including due payments
export const payments = sqliteTable("payments", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => uuidv4()),
  tenantId: text("tenant_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  branchId: text("branch_id")
    .notNull()
    .references(() => branches.id, { onDelete: "cascade" }),
  customerId: text("customer_id").references(() => customers.id),
  orderId: text("order_id").references(() => orders.id),
  amount: real("amount").notNull(),
  paymentMethod: text("payment_method")
    .notNull()
    .default("cash")
    .$type<"cash" | "card" | "bank_transfer">(),
  paymentReference: text("payment_reference"),
  paymentType: text("payment_type").notNull().$type<"sale" | "due_payment" | "due_adjustment" | "advance_payment" | "advance_used">(),
  notes: text("notes"),
  performedBy: text("performed_by").references(() => users.id),
  date: integer("date", { mode: "timestamp" })
    .notNull()
    .$defaultFn(() => new Date()),
  createdAt: integer("created_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
  updatedAt: integer("updated_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
});

// Define payments relationships
export const paymentsRelations = relations(payments, ({ one }) => ({
  tenant: one(users, {
    fields: [payments.tenantId],
    references: [users.id],
  }),
  tenantLicense: one(tenants, {
    fields: [payments.tenantId],
    references: [tenants.userId],
    relationName: "payments"
  }),
  branch: one(branches, {
    fields: [payments.branchId],
    references: [branches.id],
  }),
  customer: one(customers, {
    fields: [payments.customerId],
    references: [customers.id],
  }),
  order: one(orders, {
    fields: [payments.orderId],
    references: [orders.id],
  }),
  performer: one(users, {
    fields: [payments.performedBy],
    references: [users.id],
  }),
}));

// Refund reasons table
export const refundReasons = sqliteTable("refund_reasons", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => uuidv4()),
  name: text("name").notNull(),
  description: text("description"),
  isActive: integer("is_active", { mode: "boolean" }).default(true),
  createdAt: integer("created_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
  updatedAt: integer("updated_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
});

// Refunds table to track product refunds
export const refunds = sqliteTable("refunds", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => uuidv4()),
  tenantId: text("tenant_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  branchId: text("branch_id")
    .notNull()
    .references(() => branches.id, { onDelete: "cascade" }),
  orderId: text("order_id").references(() => orders.id),
  customerId: text("customer_id").references(() => customers.id),
  reasonId: text("reason_id").references(() => refundReasons.id),
  refundDate: integer("refund_date", { mode: "timestamp" })
    .notNull()
    .$defaultFn(() => new Date()),
  totalAmount: real("total_amount").notNull(),
  notes: text("notes"),
  isDamaged: integer("is_damaged", { mode: "boolean" }).default(false),
  performedBy: text("performed_by").references(() => users.id),
  createdAt: integer("created_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
  updatedAt: integer("updated_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
});

// Refund items for each product in a refund
export const refundItems = sqliteTable("refund_items", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => uuidv4()),
  refundId: text("refund_id")
    .notNull()
    .references(() => refunds.id, { onDelete: "cascade" }),
  productId: text("product_id")
    .notNull()
    .references(() => products.id),
  quantity: integer("quantity").notNull(),
  unitPrice: real("unit_price").notNull(),
  total: real("total").notNull(),
  isDamaged: integer("is_damaged", { mode: "boolean" }).default(false),
  createdAt: integer("created_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
  updatedAt: integer("updated_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
});

// Define refund relationships
export const refundsRelations = relations(refunds, ({ one, many }) => ({
  tenant: one(users, {
    fields: [refunds.tenantId],
    references: [users.id],
  }),
  branch: one(branches, {
    fields: [refunds.branchId],
    references: [branches.id],
  }),
  order: one(orders, {
    fields: [refunds.orderId],
    references: [orders.id],
  }),
  customer: one(customers, {
    fields: [refunds.customerId],
    references: [customers.id],
  }),
  reason: one(refundReasons, {
    fields: [refunds.reasonId],
    references: [refundReasons.id],
  }),
  items: many(refundItems),
  performer: one(users, {
    fields: [refunds.performedBy],
    references: [users.id],
    relationName: "refundPerformer"
  }),
}));

// Define refund items relationships
export const refundItemsRelations = relations(refundItems, ({ one }) => ({
  refund: one(refunds, {
    fields: [refundItems.refundId],
    references: [refunds.id],
  }),
  product: one(products, {
    fields: [refundItems.productId],
    references: [products.id],
  }),
}));

// Expense categories table for dynamic expense headers
export const expenseCategories = sqliteTable("expense_categories", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => uuidv4()),
  tenantId: text("tenant_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  name: text("name").notNull(),
  description: text("description"),
  isActive: integer("is_active", { mode: "boolean" }).default(true),
  createdAt: integer("created_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
  updatedAt: integer("updated_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
});

// Define expense categories relationships
export const expenseCategoriesRelations = relations(expenseCategories, ({ one, many }) => ({
  tenant: one(users, {
    fields: [expenseCategories.tenantId],
    references: [users.id],
  }),
  expenses: many(expenses),
}));

// Dealers table for managing suppliers/dealers
export const dealers = sqliteTable("dealers", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => uuidv4()),
  tenantId: text("tenant_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  dealerCode: text("dealer_code"),
  name: text("name").notNull(),
  email: text("email"),
  phone: text("phone"),
  address: text("address"),
  contactPerson: text("contact_person"),
  currentBalance: real("current_balance").default(0), // Positive = we owe them, Negative = they owe us
  creditLimit: real("credit_limit").default(0),
  paymentTerms: text("payment_terms"), // e.g., "Net 30", "COD", etc.
  isActive: integer("is_active", { mode: "boolean" }).default(true),
  notes: text("notes"),
  createdAt: integer("created_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
  updatedAt: integer("updated_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
});

// Dealer purchases table to track purchases from dealers
export const dealerPurchases = sqliteTable("dealer_purchases", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => uuidv4()),
  tenantId: text("tenant_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  branchId: text("branch_id")
    .notNull()
    .references(() => branches.id, { onDelete: "cascade" }),
  dealerId: text("dealer_id")
    .notNull()
    .references(() => dealers.id, { onDelete: "cascade" }),
  purchaseNo: text("purchase_no").notNull(),
  date: integer("date", { mode: "timestamp" })
    .notNull()
    .$defaultFn(() => new Date()),
  subTotal: real("sub_total").notNull(),
  discountPercentage: real("discount_percentage").default(0),
  discountAmount: real("discount_amount").default(0),
  taxAmount: real("tax_amount").default(0),
  totalAmount: real("total_amount").notNull(),
  paidAmount: real("paid_amount").default(0),
  dueAmount: real("due_amount").default(0),
  paymentMethod: text("payment_method")
    .notNull()
    .default("cash")
    .$type<"cash" | "card" | "bank_transfer" | "credit">(),
  paymentStatus: text("payment_status")
    .notNull()
    .default("unpaid")
    .$type<"paid" | "partial" | "unpaid">(),
  status: text("status")
    .notNull()
    .default("completed")
    .$type<"pending" | "completed" | "cancelled">(),
  notes: text("notes"),
  performedBy: text("performed_by").references(() => users.id),
  createdAt: integer("created_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
  updatedAt: integer("updated_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
});

// Dealer purchase items table
export const dealerPurchaseItems = sqliteTable("dealer_purchase_items", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => uuidv4()),
  purchaseId: text("purchase_id")
    .notNull()
    .references(() => dealerPurchases.id, { onDelete: "cascade" }),
  productId: text("product_id")
    .notNull()
    .references(() => products.id),
  quantity: integer("quantity").notNull(),
  unitPrice: real("unit_price").notNull(),
  discountPercentage: real("discount_percentage").default(0),
  discountAmount: real("discount_amount").default(0),
  taxPercentage: real("tax_percentage").default(0),
  taxAmount: real("tax_amount").default(0),
  total: real("total").notNull(),
  createdAt: integer("created_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
  updatedAt: integer("updated_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
});

// Dealer payments table to track payments made to dealers
export const dealerPayments = sqliteTable("dealer_payments", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => uuidv4()),
  tenantId: text("tenant_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  branchId: text("branch_id")
    .notNull()
    .references(() => branches.id, { onDelete: "cascade" }),
  dealerId: text("dealer_id")
    .notNull()
    .references(() => dealers.id, { onDelete: "cascade" }),
  purchaseId: text("purchase_id").references(() => dealerPurchases.id), // Optional: link to specific purchase
  amount: real("amount").notNull(),
  paymentMethod: text("payment_method")
    .notNull()
    .default("cash")
    .$type<"cash" | "card" | "bank_transfer" | "cheque">(),
  paymentReference: text("payment_reference"), // Cheque number, transaction ID, etc.
  paymentType: text("payment_type")
    .notNull()
    .default("purchase_payment")
    .$type<"purchase_payment" | "advance_payment" | "adjustment">(),
  date: integer("date", { mode: "timestamp" })
    .notNull()
    .$defaultFn(() => new Date()),
  notes: text("notes"),
  performedBy: text("performed_by").references(() => users.id),
  createdAt: integer("created_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
  updatedAt: integer("updated_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
});

// Expenses table for tracking expense transactions
export const expenses = sqliteTable("expenses", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => uuidv4()),
  tenantId: text("tenant_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  branchId: text("branch_id")
    .notNull()
    .references(() => branches.id, { onDelete: "cascade" }),
  categoryId: text("category_id")
    .notNull()
    .references(() => expenseCategories.id, { onDelete: "cascade" }),
  amount: real("amount").notNull(),
  date: integer("date", { mode: "timestamp" })
    .notNull()
    .$defaultFn(() => new Date()),
  description: text("description"),
  paymentMethod: text("payment_method")
    .notNull()
    .default("cash")
    .$type<"cash" | "card" | "bank_transfer">(),
  reference: text("reference"),
  performedBy: text("performed_by").references(() => users.id),
  createdAt: integer("created_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
  updatedAt: integer("updated_at", { mode: "timestamp" }).$defaultFn(
    () => new Date()
  ),
});

// Define expenses relationships
export const expensesRelations = relations(expenses, ({ one }) => ({
  tenant: one(users, {
    fields: [expenses.tenantId],
    references: [users.id],
  }),
  branch: one(branches, {
    fields: [expenses.branchId],
    references: [branches.id],
  }),
  category: one(expenseCategories, {
    fields: [expenses.categoryId],
    references: [expenseCategories.id],
  }),
  performer: one(users, {
    fields: [expenses.performedBy],
    references: [users.id],
  }),
}));

// Define dealers relationships
export const dealersRelations = relations(dealers, ({ one, many }) => ({
  tenant: one(users, {
    fields: [dealers.tenantId],
    references: [users.id],
  }),
  purchases: many(dealerPurchases),
  payments: many(dealerPayments),
}));

// Define dealer purchases relationships
export const dealerPurchasesRelations = relations(dealerPurchases, ({ one, many }) => ({
  tenant: one(users, {
    fields: [dealerPurchases.tenantId],
    references: [users.id],
  }),
  branch: one(branches, {
    fields: [dealerPurchases.branchId],
    references: [branches.id],
  }),
  dealer: one(dealers, {
    fields: [dealerPurchases.dealerId],
    references: [dealers.id],
  }),
  items: many(dealerPurchaseItems),
  payments: many(dealerPayments),
  performer: one(users, {
    fields: [dealerPurchases.performedBy],
    references: [users.id],
  }),
}));

// Define dealer purchase items relationships
export const dealerPurchaseItemsRelations = relations(dealerPurchaseItems, ({ one }) => ({
  purchase: one(dealerPurchases, {
    fields: [dealerPurchaseItems.purchaseId],
    references: [dealerPurchases.id],
  }),
  product: one(products, {
    fields: [dealerPurchaseItems.productId],
    references: [products.id],
  }),
}));

// Define dealer payments relationships
export const dealerPaymentsRelations = relations(dealerPayments, ({ one }) => ({
  tenant: one(users, {
    fields: [dealerPayments.tenantId],
    references: [users.id],
  }),
  branch: one(branches, {
    fields: [dealerPayments.branchId],
    references: [branches.id],
  }),
  dealer: one(dealers, {
    fields: [dealerPayments.dealerId],
    references: [dealers.id],
  }),
  purchase: one(dealerPurchases, {
    fields: [dealerPayments.purchaseId],
    references: [dealerPurchases.id],
  }),
  performer: one(users, {
    fields: [dealerPayments.performedBy],
    references: [users.id],
  }),
}));

// Verification tokens for authentication
export const verificationTokens = sqliteTable("verification_tokens", {
  identifier: text("identifier").notNull(),
  token: text("token").notNull(),
  expires: integer("expires", { mode: "timestamp" }).notNull(),
});
